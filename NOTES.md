# Notes

<!-- Explore Page -->

- Featured Challenge (Problem of the Day)
- Move the tags under the search bar
- Topics to Explore cards par progress bar add
- Weekly pick (hard code the card)
- Leaderboard (redesign the component) (put daily,weekly,all time tab also)
- Live Solving (low priority)

<!-- Problem Solve Page -->

- Fix the next and prev button
- Like, Save, Share - Make it working
- Make the settings dropdown menu functional (low proirity)

<!-- Sheets Creation Page -->

- Problems checkboxes list to add
