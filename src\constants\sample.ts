import { ProblemFormData } from "@/pages/CreateProblemPage";

// --- Sample Data and Load Functionality ---
export const sampledData: ProblemFormData = {
  title: "Add Two Numbers",
  description: "Given two integers, `a` and `b`, return their sum.",
  difficulty: "Easy",
  tags: ["Math", "Basic"],
  examples: [
    {
      input: "2 3",
      output: "5",
      explanation: "2 + 3 = 5",
    },
    {
      input: "10 -5",
      output: "5",
      explanation: "10 + (-5) = 5",
    },
  ],
  constraints: "-1000 <= a, b <= 1000",
  testcases: [
    {
      input: "2 3",
      output: "5",
    },
    {
      input: "10 -5",
      output: "5",
    },
    {
      input: "-7 4",
      output: "-3",
    },
    {
      input: "0 0",
      output: "0",
    },
    {
      input: "1000 1000",
      output: "2000",
    },
  ],
  codeSnippets: {
    JAVASCRIPT:
      "const fs = require('fs');\n\nfunction addTwoNumbers(a, b) {\n    // Write your code here\n    // Return the sum of a and b\n    return a + b;\n}\n\n// Reading input from stdin (using fs to read all input)\nconst input = fs.readFileSync(0, 'utf-8').trim();\nconst [a, b] = input.split(' ').map(Number);\n\nconsole.log(addTwoNumbers(a, b));",
    PYTHON:
      "def add_two_numbers(a, b):\n    # Write your code here\n    # Return the sum of a and b\n    return a + b\n\nimport sys\ninput_line = sys.stdin.read()\na, b = map(int, input_line.split())\nprint(add_two_numbers(a, b))",
    JAVA: "import java.util.Scanner;\n\npublic class Main {\n    public static int addTwoNumbers(int a, int b) {\n        // Write your code here\n        // Return the sum of a and b\n        return a + b;\n    }\n\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int a = sc.nextInt();\n        int b = sc.nextInt();\n        System.out.println(addTwoNumbers(a, b));\n    }\n}",
    CPP: "#include <iostream>\n\nusing namespace std;\n\nint addTwoNumbers(int a, int b) {\n    // Write your code here\n    // Return the sum of a and b\n    return a + b;\n}\n\nint main() {\n    int a, b;\n    cin >> a >> b;\n    cout << addTwoNumbers(a, b) << endl;\n    return 0;\n}",
  },
  referenceSolutions: {
    JAVASCRIPT:
      "const fs = require('fs');\n\n// Reading input from stdin (using fs to read all input)\nconst input = fs.readFileSync(0, 'utf-8').trim();\nconst [a, b] = input.split(' ').map(Number);\n\nconsole.log(a + b);",
    PYTHON:
      "import sys\ninput_line = sys.stdin.read()\na, b = map(int, input_line.split())\nprint(a + b)",
    JAVA: "import java.util.Scanner;\n\npublic class Main {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int a = sc.nextInt();\n        int b = sc.nextInt();\n        System.out.println(a + b);\n    }\n}",
    CPP: "#include <iostream>\n\nusing namespace std;\n\nint main() {\n    int a, b;\n    cin >> a >> b;\n    cout << a + b << endl;\n    return 0;\n}",
  },
  hints: [
    "You need to read the input from stdin.",
    "The input will be two space-separated integers on a single line.",
    "Return the sum of the two integers.",
  ],
  editorial: {
    explanation:
      "The problem requires reading two integers from standard input and returning their sum. The provided code snippets demonstrate how to read from stdin in each language.",
    timeComplexity: "O(1)",
    spaceComplexity: "O(1)",
    code: "#include <iostream>\n\nusing namespace std;\n\nint main() {\n    int a, int b;\n    cin >> a >> b;\n    cout << a + b << endl;\n    return 0;\n}",
    codeLanguage: "CPP",
  },
  companyTag: "Facebook, Apple",
};

export const sampleStringProblem: ProblemFormData = {
  title: "Reverse String",
  description:
    "Write a function that reverses a string. The input string is given as an array of characters `s`.\nYou must do this by modifying the input array `in-place` with O(1) extra memory.",
  difficulty: "Easy",
  tags: ["String", "Two Pointers", "In-place"],
  examples: [
    {
      input: "hello",
      output: "olleh",
      explanation: "The reversed string of 'hello' is 'olleh'.",
    },
    {
      input: "Hannah",
      output: "hannaH",
      explanation: "The reversed string of 'Hannah' is 'hannaH'.",
    },
  ],
  constraints: "1 <= s.length <= 10^5\ns[i] is a printable ASCII character.",
  testcases: [
    {
      input: "hello",
      output: "olleh",
    },
    {
      input: "Hannah",
      output: "hannaH",
    },
    {
      input: "a",
      output: "a",
    },
    {
      input: "",
      output: "",
    },
    {
      input: "racecar",
      output: "racecar",
    },
  ],
  codeSnippets: {
    JAVASCRIPT:
      "// Javascript Code Snippet\nfunction reverseString(s) {\n    // Write your code here\n    // Modify the array s in-place instead.\n}",
    PYTHON:
      '# Python Code Snippet\ndef reverseString(s: list[str]) -> None:\n    """Do not return anything, modify s in-place instead."""\n    # Write your code here\n    pass',
    JAVA: "// Java Code Snippet\nclass Solution {\n    public void reverseString(char[] s) {\n        // Write your code here\n        // Do not return anything, modify s in-place instead.\n    }\n}",
    CPP: "// C++ Code Snippet\n#include <vector>\n\nclass Solution {\npublic:\n    void reverseString(std::vector<char>& s) {\n        // Write your code here\n        // Do not return anything, modify s in-place instead.\n    }\n};",
  },
  referenceSolutions: {
    JAVASCRIPT:
      "// Javascript Reference Solution\nfunction reverseString(s) {\n    let left = 0;\n    let right = s.length - 1;\n\n    while (left < right) {\n        [s[left], s[right]] = [s[right], s[left]];\n        left++;\n        right--;\n    }\n}",
    PYTHON:
      "# Python Reference Solution\ndef reverseString(s: list[str]) -> None:\n    left, right = 0, len(s) - 1\n    while left < right:\n        s[left], s[right] = s[right], s[left]\n        left += 1\n        right -= 1",
    JAVA: "// Java Reference Solution\nclass Solution {\n    public void reverseString(char[] s) {\n        int left = 0;\n        int right = s.length - 1;\n\n        while (left < right) {\n            char temp = s[left];\n            s[left] = s[right];\n            s[right] = temp;\n            left++;\n            right--;\n        }\n    }\n}",
    CPP: "// C++ Reference Solution\n#include <vector>\n\nclass Solution {\npublic:\n    void reverseString(std::vector<char>& s) {\n        int left = 0;\n        int right = s.size() - 1;\n\n        while (left < right) {\n            std::swap(s[left], s[right]);\n            left++;\n            right--;\n        }\n    }\n};",
  },
  hints: [
    "Use the two-pointer technique.",
    "Initialize two pointers, one at the beginning and one at the end of the array.",
    "Swap the characters at the two pointers and move the pointers towards the middle of the array.",
  ],
  editorial: {
    explanation:
      "The two-pointer technique is used to reverse the string in-place.  We initialize two pointers, `left` at the beginning of the array and `right` at the end.  We swap the characters at `left` and `right` and move the pointers towards the middle until `left` and `right` cross each other.",
    timeComplexity: "O(N), where N is the length of the string.",
    spaceComplexity: "O(1)",
    code: "#include <vector>\n\nclass Solution {\npublic:\n    void reverseString(std::vector<char>& s) {\n        int left = 0;\n        int right = s.size() - 1;\n\n        while (left < right) {\n            std::swap(s[left], s[right]);\n            left++;\n            right--;\n        }\n    }\n};",
    codeLanguage: "CPP",
  },
  companyTag: "Apple, Facebook",
};
